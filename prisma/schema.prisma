generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model bannerdata {
  id          Int     @id @default(autoincrement())
  title       String? @db.Var<PERSON>har(255)
  banner_text String? @db.VarChar(255)
  imgpath     String? @db.VarChar(255)
}

model sponserimg {
  id      Int     @id @default(autoincrement())
  imgpath String? @db.VarChar(255)
  title   String? @db.VarChar(250)
}

model dresstyle {
  id       Int     @id @default(autoincrement())
  title    String? @db.VarChar(255)
  imgpath  String? @db.VarChar(255)
  colstart String? @db.VarChar(255)
  colspan  String? @db.VarChar(255)
}

model dresscategory {
  id         String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  category   String?      @db.<PERSON>ar<PERSON>har(250)
  dressclass dressclass[]
}

model dressclass {
  id            String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  class         String?        @db.VarChar(250)
  parenttable   String?        @db.Uuid
  dresscategory dresscategory? @relation(fields: [parenttable], references: [id], onDelete: Cascade, onUpdate: NoAction)
  products      products[]
}

model products {
  id          String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  title       String?     @db.VarChar(250)
  rating      Decimal?    @db.Decimal(4, 2)
  regular     Decimal?    @db.Decimal(4, 0)
  discount    Decimal?    @db.Decimal(4, 0)
  discrate    Decimal?    @db.Decimal(4, 2)
  items       String?     @db.VarChar(250)
  details     String?
  brandimg    String?     @db.VarChar(250)
  brandtitle  String?     @db.VarChar(250)
  comments    String?
  parenttable String?     @db.Uuid
  dressclass  dressclass? @relation(fields: [parenttable], references: [id], onDelete: Cascade, onUpdate: NoAction)
}
