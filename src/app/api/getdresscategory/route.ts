import { NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";

const prisma = new PrismaClient();

export async function GET(){
    const getData = await prisma.dresscategory.findMany({
        select:{
            id:true,
            category:true,
            dressclass:{
                select:{
                    id:true,
                    class:true,
                    parenttable:true
                }
            }
        }
    });

    const configArr = getData.map(({dressclass,...rest})=>({...rest,class:dressclass}));

    return NextResponse.json(configArr);
}