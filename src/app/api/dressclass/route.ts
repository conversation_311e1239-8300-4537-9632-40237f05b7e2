import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";

type StoreData = {
    class : string,
    parenttable : string
}

const prisma = new PrismaClient();

export async function POST(req:NextRequest){
    const formData = await req.formData();
    const storeData = {} as StoreData;

    formData.forEach((value,key)=>{
        storeData[key as keyof StoreData] = typeof value === "string" ? value.toString() : "";
    });

    await prisma.dressclass.create({
        data:storeData
    });

    return NextResponse.json({message:"added data"},{status:200})
}