import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";

const prisma = new PrismaClient();

export async function DELETE(req: NextRequest){
    const {searchParams} = new URL(req.url);
    const id = searchParams.get("id")?.toString();

    await prisma.dresscategory.delete({
        where:{
            id: id
        }
    });

    return NextResponse.json({message:"removed"},{status:200});
}