import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";

const prisma = new PrismaClient();

type StoreData = {
    id: string,
    class:string
}

export async function PUT(req:NextRequest){
    const formData = await req.formData();
    const storeData = {} as StoreData;

    formData.forEach((value,key)=>{
        if(typeof value == "string"){
            storeData[key as keyof StoreData] = value
        }
    });

    await prisma.dressclass.update({
        where:{
            id: storeData.id.toString()
        },
        data:{
            class: storeData.class.toString()
        }
    });

    return NextResponse.json({message:"updated"},{status:200})
}