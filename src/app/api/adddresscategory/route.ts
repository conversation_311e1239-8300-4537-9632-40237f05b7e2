import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";

type StoreData = {
    [key:string] : string | File | null,
}

const prisma = new PrismaClient();

export async function POST(req: NextRequest){
    const formData = await req.formData();
    const storeData = {} as StoreData;

    formData.forEach((value,key)=>{
        if(typeof key === "string"){
            storeData[key] = typeof value === "string" ? value : "";
        }
    });

    await prisma.dresscategory.create({
        data: storeData
    });

    return NextResponse.json({message:storeData});
}