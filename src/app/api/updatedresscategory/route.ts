import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";

type DataArr = {
    id: string,
    category: string 
}

const prisma = new PrismaClient();

export async function PUT(req:NextRequest){
    const formData = await req.formData();
    const dataArr = {} as DataArr;

    formData.forEach((value,key)=>{
        if(typeof value === "string"){
            dataArr[key as keyof DataArr] = value;
        }
    });

    await prisma.dresscategory.update({
        where:{
            id: dataArr.id
        },
        data:{
            category: dataArr.category
        }
    });

    return NextResponse.json({message:"updated"},{status:200})
}