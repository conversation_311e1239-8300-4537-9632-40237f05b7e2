"use client";

import ClientLoading from "@/app/(clientSide)/loading";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { <PERSON>, <PERSON>rop<PERSON>} from "next/font/google";
import Image from "next/image";
import { useState } from "react";
import { FaPlus } from "react-icons/fa";

type ImageContainer = {
    id: number,
    imgpath:string | null,
    title:string | null
}

type BrandDetails = {
    imgpath: File | null,
    title: string | null
}

const anton = Anton({
    subsets:['latin'],
    weight:'400'
});

const manrope= Manrope({
    subsets:["latin"]
});

export default function Sponser(){
    const queryClient = useQueryClient();

    const addSponserImg = useMutation({
        mutationFn:async (formData:FormData)=>{
            await axios.post("/api/sponseradddata",formData)
            .then((res)=>{console.log(res.status)});

            addItems();
        },
        onSuccess:()=>{
            queryClient.invalidateQueries({queryKey:['sponserImg']})
        }
    });

    const removeSponserImg = useMutation({
        mutationFn:async (wrap:{id:number,imgpath:string})=>{
            await axios.delete("/api/sponserdataremove",{params:{id:wrap.id,imgpath:wrap.imgpath}})
            .then((res)=>{console.log(res)})
        },
        onSuccess:()=>{
            queryClient.invalidateQueries({queryKey:['sponserImg']})
        }
    });

    const [currentItems,setCurrentItems] = useState([0]);
    const [imgPreview,setImgPreview] = useState<ImageContainer[]>([]);
    const [brandDetails,setBrandDetails] = useState<BrandDetails>({
        title:null,
        imgpath:null
    }); 

    const {isPending,isError,data} = useQuery({
        queryKey:["sponserImg"],
        queryFn:async ()=>{
            const response = await axios("/api/sponserdataretrieve");
            const result = response.data.data;
            const len = result.length;
            let arr: number[] = [];

            for(let repeat=0; repeat <= len; repeat++){
               arr = [...arr,repeat]
            }

            setCurrentItems(arr);
            setImgPreview(result);

            return result;
        }
    })

    const addItems=()=>{
        const currentValue = Number(currentItems[currentItems.length - 1]);
        const newValue = currentValue + 1;

        setCurrentItems([...currentItems,newValue]);
    }

    const boxHandler=(event: React.ChangeEvent<HTMLInputElement>)=>{
        const {name,value,files} = event.target;

        if(name == "title"){
            if(!files){
                setBrandDetails({...brandDetails,title:value})
            }
        }else{
            setBrandDetails({...brandDetails,imgpath:files?.[0] ?? null})
        }
    }

    const addSponser=()=>{
        const formData = new FormData();

        formData.append("imgpath",brandDetails.imgpath ?? "");
        formData.append("title",brandDetails.title ?? "");

        addSponserImg.mutate(formData);

        setBrandDetails({imgpath:null,title:null});
    }
    return(
        <>
        <section className="mt-20">
            <div className="px-5 mb-20">
                <h2 className={`${anton.className} capitalize text-5xl font-semibold`} style={{WebkitTextStroke:'2px black',WebkitTextFillColor:"transparent"}}>
                    add your sponser image
                </h2>
            </div>
            <div className={`grid gap-x-5 gap-y-20 px-5 ${isPending?"grid-cols-1":"grid-cols-5"}`}>
                {
                    isPending?
                    <div className="py-5 w-full flex justify-center items-center">
                        <ClientLoading/>
                    </div>:
                    isError?
                    <div className="py-5 w-full flex justify-center items-center">
                        <h4>
                            Something went wrong
                        </h4>
                    </div>:
                    currentItems.map((items,index)=>{
                        return <div className="h-20 w-full bg-white/20 backdrop-blur-[5px] shadow-sm rounded-lg flex flex-col justify-center items-center px-5 relative group" key={items}>
                            <div className="absolute -top-7 w-full group/title">
                                <span className={`${manrope.className} text-black/20 transition-all duration-200 ease-linear group-hover/title:font-bold group-hover/title:text-black/80`}>
                                    {imgPreview[items] ? imgPreview[items].title : null}
                                </span>
                                
                            </div>
                            {
                                imgPreview[items] ?
                                <>
                                    <Image src={imgPreview[items].imgpath ?? ""} alt="sponserImg" height={36} width={127}/>
                                    <div className="absolute -bottom-8 right-2">
                                <button className={`${anton.className} text-sm px-3 py-1 bg-white shadow-lg rounded-lg transition-all duration-200 ease-linear opacity-0 group-hover:opacity-100 hover:cursor-pointer hover:text-rose-600`} onClick={()=>{removeSponserImg.mutate({id:imgPreview[items].id,imgpath:imgPreview[items].imgpath ?? ""})}}>
                                    remove
                                </button>
                            </div>
                            </>
                                    :
                                    <>
                                    <label htmlFor={String(items)} className="h-[60px] w-full rounded-lg border border-gray-400/50 flex justify-center items-center group">
                                    <input type="file" id={String(items)} accept="image/*" className="h-full w-full hidden" name="imgpath" onChange={boxHandler}/>
                                    <FaPlus className="group-hover:scale-90"/>

                                    {
                                        brandDetails.imgpath instanceof File ?
                                        <Image src={URL.createObjectURL(brandDetails.imgpath)} fill alt="brandImg"/>: null
                                    }
                                    </label>

                                    <div className="absolute top-22 w-full h-[25px] flex flex-row gap-x-5">
                                        <button className={`${manrope.className} bg-[#2ecc71] px-2 text-white rounded-lg transition-all duration-150 ease-linear hover:bg-[#27ae60]`} onClick={addSponser}>
                                            add
                                        </button>
                                        <input type="text" className={`${manrope.className} h-full w-[80%] bg-white rounded-sm shadow-sm shadow-black/20 placeholder:text-md px-2 placeholder:font-medium placeholder:text-black/30 text-black/80 font-medium focus:outline-black/20`} placeholder="brand name" name="title" onChange={(event)=>{boxHandler(event)}}/>
                                    </div>
                                    </>
                            }
                            </div>
                    })
                }
            </div>
        </section>
        </>
    )
}